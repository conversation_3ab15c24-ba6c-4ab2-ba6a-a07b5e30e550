'use client';

import { ReactNode } from 'react';
import Head from 'next/head';
import Enhanced<PERSON><PERSON>bar from './EnhancedNavbar';
import EnhancedFooter from './EnhancedFooter';
import { motion } from 'framer-motion';

interface PageLayoutProps {
  children: ReactNode;
  title?: string;
  description?: string;
  keywords?: string;
  customStyles?: string;
}

const PageLayout = ({ 
  children, 
  title = 'NextGen Youth Movement',
  description = 'Empowering tomorrow\'s leaders through faith-based youth development, mentorship, and community engagement.',
  keywords = 'youth, leadership, mentorship, community, faith, development, next generation',
  customStyles = ''
}: PageLayoutProps) => {
  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5
  };

  return (
    <>
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <meta name="keywords" content={keywords} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="index, follow" />
        <meta name="author" content="NextGen Youth Movement" />
        
        {/* Open Graph / Facebook */}
        <meta property="og:type" content="website" />
        <meta property="og:title" content={title} />
        <meta property="og:description" content={description} />
        <meta property="og:site_name" content="NextGen Youth Movement" />
        
        {/* Twitter */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content={title} />
        <meta name="twitter:description" content={description} />
        
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        
        {/* Theme Color */}
        <meta name="theme-color" content="#00D9FF" />
        <meta name="msapplication-TileColor" content="#00D9FF" />
      </Head>

      <div className={`min-h-screen flex flex-col ${customStyles}`}>
        <EnhancedNavbar />
        
        <motion.main
          initial="initial"
          animate="in"
          exit="out"
          variants={pageVariants}
          transition={pageTransition}
          className="flex-grow pt-16"
        >
          {children}
        </motion.main>
        
        <EnhancedFooter />
      </div>
    </>
  );
};

export default PageLayout;
