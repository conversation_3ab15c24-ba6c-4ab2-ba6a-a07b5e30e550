'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface CoreValuesCardProps {
  icon: ReactNode;
  title: string;
  description: string;
  color: 'electric-blue' | 'neon-green' | 'bright-orange' | 'hot-pink' | 'electric-purple';
  index: number;
  isActive: boolean;
  onHover: () => void;
  onLeave: () => void;
}

const CoreValuesCard = ({
  icon,
  title,
  description,
  color,
  index,
  isActive,
  onHover,
  onLeave
}: CoreValuesCardProps) => {
  const colorClasses = {
    'electric-blue': {
      text: 'text-electric-blue',
      bg: 'bg-electric-blue',
      border: 'border-electric-blue',
      gradient: 'from-electric-blue to-cyan-400'
    },
    'neon-green': {
      text: 'text-neon-green',
      bg: 'bg-neon-green',
      border: 'border-neon-green',
      gradient: 'from-neon-green to-green-400'
    },
    'bright-orange': {
      text: 'text-bright-orange',
      bg: 'bg-bright-orange',
      border: 'border-bright-orange',
      gradient: 'from-bright-orange to-orange-400'
    },
    'hot-pink': {
      text: 'text-hot-pink',
      bg: 'bg-hot-pink',
      border: 'border-hot-pink',
      gradient: 'from-hot-pink to-pink-400'
    },
    'electric-purple': {
      text: 'text-electric-purple',
      bg: 'bg-electric-purple',
      border: 'border-electric-purple',
      gradient: 'from-electric-purple to-purple-400'
    }
  };

  const currentColor = colorClasses[color];

  return (
    <motion.div
      className="relative group cursor-pointer"
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ scale: 1.05, y: -10 }}
      onMouseEnter={onHover}
      onMouseLeave={onLeave}
    >
      <div className={`
        relative p-6 rounded-2xl bg-dark-bg border-2 ${currentColor.border}
        transition-all duration-300 group-hover:shadow-2xl backdrop-blur-sm
        overflow-hidden
      `}>
        {/* Background gradient effect */}
        <div className={`
          absolute inset-0 bg-gradient-to-br ${currentColor.gradient} 
          opacity-0 group-hover:opacity-10 transition-opacity duration-300
        `} />

        {/* Icon */}
        <motion.div
          className={`${currentColor.text} mb-4`}
          whileHover={{ rotate: 360 }}
          transition={{ duration: 0.5 }}
        >
          {icon}
        </motion.div>

        {/* Title */}
        <motion.h4
          className="text-xl font-bold text-white mb-3 group-hover:text-white transition-colors duration-300"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: index * 0.1 + 0.2 }}
        >
          {title}
        </motion.h4>

        {/* Description */}
        <motion.p
          className="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors duration-300"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ delay: index * 0.1 + 0.4 }}
        >
          {description}
        </motion.p>

        {/* Bottom accent line */}
        <motion.div
          className={`absolute bottom-0 left-0 h-1 ${currentColor.bg} transition-all duration-300`}
          initial={{ width: '0%' }}
          whileInView={{ width: '100%' }}
          transition={{ delay: index * 0.1 + 0.6, duration: 0.5 }}
        />

        {/* Hover particles */}
        {isActive && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(5)].map((_, i) => (
              <motion.div
                key={i}
                className={`absolute w-1 h-1 ${currentColor.bg} rounded-full`}
                initial={{ 
                  x: '50%', 
                  y: '50%', 
                  scale: 0,
                  opacity: 0 
                }}
                animate={{ 
                  x: `${50 + (Math.random() - 0.5) * 100}%`,
                  y: `${50 + (Math.random() - 0.5) * 100}%`,
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{ 
                  duration: 1,
                  delay: i * 0.1,
                  repeat: Infinity,
                  repeatDelay: 1
                }}
              />
            ))}
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default CoreValuesCard;
