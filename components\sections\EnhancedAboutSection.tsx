'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Star, Users, Target, Book } from 'lucide-react';
import CoreValuesCard from '../ui/CoreValuesCard';

const EnhancedAboutSection = () => {
  const [activeValue, setActiveValue] = useState<number | null>(null);

  const coreValues = [
    {
      icon: <Star size={32} />,
      title: "God-Given Potential",
      description: "We believe every young person has unique gifts and talents waiting to be discovered and developed through faith-based guidance.",
      color: "electric-blue" as const
    },
    {
      icon: <Users size={32} />,
      title: "Community Impact", 
      description: "True leadership emerges through service to others and making a positive difference in our communities and beyond.",
      color: "neon-green" as const
    },
    {
      icon: <Target size={32} />,
      title: "Transformative Growth",
      description: "Personal transformation happens through intentional mentorship, challenges, and real-world application of skills.",
      color: "bright-orange" as const
    },
    {
      icon: <Book size={32} />,
      title: "Lifelong Learning",
      description: "We foster a culture of continuous growth, curiosity, and the pursuit of excellence in all areas of life.",
      color: "hot-pink" as const
    }
  ];

  return (
    <section className="py-20 bg-dark-bg relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Mission Statement */}
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8">
            Our <span className="text-electric-blue">Mission</span>
          </h2>
          <motion.blockquote
            className="text-2xl md:text-3xl text-gray-300 max-w-4xl mx-auto leading-relaxed italic"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            "To empower teenagers to discover and develop their{' '}
            <span className="text-electric-blue font-semibold">God-given potential</span>{' '}
            through transformative leadership programs, meaningful mentorship, and impactful community engagement."
          </motion.blockquote>
        </motion.div>

        {/* Core Values */}
        <motion.div
          className="mb-20"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
        >
          <div className="text-center mb-16">
            <h3 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Core <span className="text-neon-green">Values</span>
            </h3>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              These foundational principles guide everything we do and shape how we approach youth development and leadership training.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <CoreValuesCard
                key={value.title}
                {...value}
                index={index}
                isActive={activeValue === index}
                onHover={() => setActiveValue(index)}
                onLeave={() => setActiveValue(null)}
              />
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default EnhancedAboutSection;
