'use client';

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger, TextPlugin);
}

// GSAP Animation Utilities for NextGen Youth Movement
export const gsapUtils = {
  // Fade in animation
  fadeIn: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.fromTo(element, 
      { opacity: 0, y: 50 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8, 
        ease: "power2.out",
        ...options 
      }
    );
  },

  // Scroll-triggered animations
  scrollTrigger: {
    fadeInOnScroll: (element: string | Element, options: ScrollTrigger.Vars = {}) => {
      return gsap.fromTo(element,
        { opacity: 0, y: 100 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
            ...options
          }
        }
      );
    }
  }
};

export default gsapUtils;
