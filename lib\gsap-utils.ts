'use client';

import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { TextPlugin } from 'gsap/TextPlugin';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger, TextPlugin);
}

// GSAP Animation Utilities for NextGen Youth Movement
export const gsapUtils = {
  // Fade in animation
  fadeIn: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.fromTo(element, 
      { opacity: 0, y: 50 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.8, 
        ease: "power2.out",
        ...options 
      }
    );
  },

  // Scroll-triggered animations
  scrollTrigger: {
    fadeInOnScroll: (element: string | Element, options: ScrollTrigger.Vars = {}) => {
      return gsap.fromTo(element,
        { opacity: 0, y: 100 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
            ...options
          }
        }
      );
    },

    countUp: (element: string | Element, endValue: number, options: ScrollTrigger.Vars = {}) => {
      const obj = { value: 0 };
      return gsap.to(obj, {
        value: endValue,
        duration: 2,
        ease: "power2.out",
        onUpdate: () => {
          if (element instanceof Element) {
            element.textContent = Math.round(obj.value).toString();
          } else {
            const el = document.querySelector(element);
            if (el) el.textContent = Math.round(obj.value).toString();
          }
        },
        scrollTrigger: {
          trigger: element,
          start: "top 80%",
          toggleActions: "play none none none",
          ...options
        }
      });
    }
  },

  // Stagger animation for multiple elements
  staggerIn: (elements: string | Element[], options: gsap.TweenVars = {}) => {
    return gsap.fromTo(elements,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out",
        stagger: 0.1,
        ...options
      }
    );
  },

  // Scale animation
  scaleIn: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.fromTo(element,
      { opacity: 0, scale: 0.5 },
      {
        opacity: 1,
        scale: 1,
        duration: 0.6,
        ease: "back.out(1.7)",
        ...options
      }
    );
  },

  // Slide in from left
  slideInLeft: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.fromTo(element,
      { opacity: 0, x: -100 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: "power2.out",
        ...options
      }
    );
  },

  // Slide in from right
  slideInRight: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.fromTo(element,
      { opacity: 0, x: 100 },
      {
        opacity: 1,
        x: 0,
        duration: 0.8,
        ease: "power2.out",
        ...options
      }
    );
  },

  // Floating animation
  float: (element: string | Element, options: gsap.TweenVars = {}) => {
    return gsap.to(element, {
      y: -10,
      duration: 2,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1,
      ...options
    });
  },

  // Hero section animation sequence
  heroSequence: (elements: { [key: string]: string | Element }) => {
    const tl = gsap.timeline();
    
    if (elements.title) {
      tl.fromTo(elements.title, 
        { opacity: 0, scale: 0.5 },
        { opacity: 1, scale: 1, duration: 1, ease: "back.out(1.7)" }
      );
    }
    
    if (elements.subtitle) {
      tl.fromTo(elements.subtitle,
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 0.8, ease: "power2.out" },
        "-=0.5"
      );
    }
    
    if (elements.cta) {
      tl.fromTo(elements.cta,
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 0.6, ease: "power2.out" },
        "-=0.3"
      );
    }

    return tl;
  },

  // Create timeline
  createTimeline: (options: gsap.TimelineVars = {}) => {
    return gsap.timeline(options);
  },

  // Particle animation
  createParticles: (container: string | Element, count: number = 20) => {
    const containerEl = typeof container === 'string' ? document.querySelector(container) : container;
    if (!containerEl) return;

    for (let i = 0; i < count; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 bg-electric-blue rounded-full opacity-30';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      containerEl.appendChild(particle);

      gsap.to(particle, {
        x: (Math.random() - 0.5) * 200,
        y: (Math.random() - 0.5) * 200,
        scale: Math.random() * 1.5 + 0.5,
        duration: Math.random() * 3 + 2,
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1,
        delay: Math.random() * 2
      });
    }
  }
};

export default gsapUtils;
