import type { <PERSON>ada<PERSON> } from "next";
import { Arvo } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";

const arvo = Arvo({
  variable: "--font-arvo",
  subsets: ["latin"],
  weight: ["400", "700"],
});

export const metadata: Metadata = {
  title: "NextGen Youth Movement",
  description: "Empowering tomorrow's leaders through faith-based youth development, mentorship, and community engagement.",
  keywords: "youth leadership, mentorship, faith-based development, community engagement, next generation leaders",
  authors: [{ name: "NextGen Youth Movement" }],
  creator: "NextGen Youth Movement",
  publisher: "NextGen Youth Movement",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://nextgenyouth.org",
    siteName: "NextGen Youth Movement",
    title: "NextGen Youth Movement - Empowering Tomorrow's Leaders",
    description: "Discover your God-given potential through faith-based youth development, mentorship, and community engagement.",
  },
  twitter: {
    card: "summary_large_image",
    title: "NextGen Youth Movement",
    description: "Empowering tomorrow's leaders through faith-based youth development.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${arvo.variable} antialiased font-arvo`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
