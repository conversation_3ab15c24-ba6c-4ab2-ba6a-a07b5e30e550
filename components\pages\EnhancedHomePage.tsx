'use client';

import { useEffect, useRef } from 'react';
import Link from 'next/link';
import { <PERSON>R<PERSON>, Spark<PERSON>, Target, Users, Heart } from 'lucide-react';
import EnhancedStatsSection from '../ui/EnhancedStatsSection';
import gsapUtils from '../../lib/gsap-utils';

const EnhancedHomePage = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // GSAP Hero Animation Sequence
    if (titleRef.current && subtitleRef.current && ctaRef.current) {
      gsapUtils.heroSequence({
        title: titleRef.current,
        subtitle: subtitleRef.current,
        cta: ctaRef.current
      });
    }

    // Floating particles animation
    gsapUtils.createParticles('.hero-particles', 15);

    // Stagger animation for feature cards
    gsapUtils.staggerIn('.feature-card', { delay: 1 });

  }, []);

  const features = [
    {
      icon: <Target className="w-8 h-8" />,
      title: "Purpose-Driven Leadership",
      description: "Discover your unique calling and develop the skills to lead with purpose and integrity.",
      color: "electric-blue"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Community Impact",
      description: "Make a real difference in your community through hands-on service projects and initiatives.",
      color: "neon-green"
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Mentorship & Growth",
      description: "Connect with experienced mentors who will guide your personal and spiritual development.",
      color: "bright-orange"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "Transformative Programs",
      description: "Participate in life-changing workshops, conferences, and leadership development programs.",
      color: "hot-pink"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section 
        ref={heroRef}
        className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-dark-bg via-dark-bg to-electric-blue/10"
      >
        {/* Animated Background Particles */}
        <div className="hero-particles absolute inset-0"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Main Headline */}
          <h1
            ref={titleRef}
            className="text-5xl md:text-7xl font-arvo font-bold text-white leading-tight mb-8"
          >
            Empowering{' '}
            <span className="gradient-text">
              Tomorrow's
            </span>{' '}
            Leaders
          </h1>

          {/* Subtitle */}
          <p
            ref={subtitleRef}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed font-arvo"
          >
            Discover your God-given potential through transformative leadership programs, 
            meaningful mentorship, and impactful community engagement.
          </p>

          {/* CTA Buttons */}
          <div
            ref={ctaRef}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <Link href="/programs">
              <div className="btn-primary group">
                <Sparkles size={24} />
                <span>Explore Programs</span>
                <ArrowRight size={24} className="group-hover:translate-x-1 transition-transform" />
              </div>
            </Link>

            <Link href="/about">
              <div className="px-8 py-4 border-2 border-electric-blue text-electric-blue font-semibold rounded-lg hover:bg-electric-blue hover:text-white transition-all duration-300 font-arvo">
                Learn Our Story
              </div>
            </Link>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
          <div className="w-6 h-10 border-2 border-electric-blue rounded-full flex justify-center">
            <div className="w-1 h-3 bg-electric-blue rounded-full mt-2 animate-bounce"></div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-r from-dark-bg to-dark-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-arvo font-bold text-white mb-6">
              Why Choose <span className="gradient-text">NextGen</span>?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're more than just a youth program. We're a movement dedicated to unlocking 
              the incredible potential within every young person.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className="feature-card bg-gray-900/80 backdrop-blur-sm border border-gray-700 rounded-xl p-6 text-center hover-lift group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-${feature.color} to-${feature.color}/70 text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                
                <h3 className="text-xl font-arvo font-bold text-white mb-3">
                  {feature.title}
                </h3>
                
                <p className="text-gray-400 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Statistics Section */}
      <EnhancedStatsSection />

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-black">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-gradient-to-r from-gray-900/50 to-black/50 backdrop-blur-sm border border-gray-700 rounded-2xl p-12 max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-arvo font-bold text-white mb-6">
              Ready to Discover Your <span className="gradient-text">Potential</span>?
            </h2>
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Join hundreds of teenagers who have already started their transformation journey 
              with NextGen Youth Movement. Your future starts here.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <div className="btn-primary">
                  <span>Get Started Today</span>
                  <ArrowRight size={20} />
                </div>
              </Link>
              <Link href="/programs">
                <div className="px-8 py-3 border-2 border-neon-green text-neon-green font-semibold rounded-lg hover:bg-neon-green hover:text-black transition-all duration-300 font-arvo">
                  View Programs
                </div>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default EnhancedHomePage;
