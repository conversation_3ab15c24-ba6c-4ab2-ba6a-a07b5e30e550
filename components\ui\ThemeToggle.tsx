'use client';

import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Sun, Moon, Monitor } from 'lucide-react';

const ThemeToggle = () => {
  const [mounted, setMounted] = useState(false);
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  const themes = [
    { name: 'light', icon: Sun, label: 'Light' },
    { name: 'dark', icon: Moon, label: 'Dark' },
    { name: 'system', icon: Monitor, label: 'System' }
  ];

  return (
    <div className="relative">
      <div className="flex items-center space-x-1 bg-background/80 backdrop-blur-md border border-border rounded-lg p-1">
        {themes.map(({ name, icon: Icon, label }) => (
          <button
            key={name}
            onClick={() => setTheme(name)}
            className={`
              relative flex items-center justify-center w-8 h-8 rounded-md transition-all duration-200
              ${theme === name 
                ? 'bg-primary text-primary-foreground shadow-sm' 
                : 'text-muted-foreground hover:text-foreground hover:bg-muted'
              }
            `}
            title={label}
          >
            <Icon size={16} />
            {theme === name && (
              <div className="absolute inset-0 rounded-md bg-gradient-to-r from-electric-blue/20 to-bright-orange/20 animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ThemeToggle;
